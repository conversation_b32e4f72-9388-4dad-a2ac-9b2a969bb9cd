{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_895135be.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XBnWTXQTpZuckHnYdyfrhWX2ugBIK4p1ITOuQN7l/Wo=", "__NEXT_PREVIEW_MODE_ID": "fa4a9485a6987bc10bef9273d222c1c3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a0649a0c0fb1835d891618de05660abf5d11ab17ba4c4cb68fc9746fc3b648ec", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "052f9fc9609561e8df81ff715ec81603b9fa7c6e031c4271489261f92ee918fd"}}}, "instrumentation": null, "functions": {}}