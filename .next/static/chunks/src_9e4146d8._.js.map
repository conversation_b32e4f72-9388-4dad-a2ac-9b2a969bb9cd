{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Legacy client for backward compatibility (client-side only)\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types (will be generated later)\nexport type Database = {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          created_at: string\n          subscription_tier: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          created_at?: string\n          subscription_tier?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          created_at?: string\n          subscription_tier?: string\n        }\n      }\n      // More tables will be added as we implement them\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist } from 'zustand/middleware'\n\n// User and Authentication Store\ninterface User {\n  id: string\n  email: string\n  subscription_tier: 'free' | 'pro' | 'team' | 'enterprise'\n}\n\ninterface AuthState {\n  user: User | null\n  isLoading: boolean\n  setUser: (user: User | null) => void\n  setLoading: (loading: boolean) => void\n  logout: () => void\n}\n\nexport const useAuthStore = create<AuthState>()(\n  devtools(\n    persist(\n      (set) => ({\n        user: null,\n        isLoading: true,\n        setUser: (user) => set({ user }),\n        setLoading: (isLoading) => set({ isLoading }),\n        logout: () => set({ user: null }),\n      }),\n      {\n        name: 'auth-storage',\n        partialize: (state) => ({ user: state.user }),\n      }\n    ),\n    { name: 'auth-store' }\n  )\n)\n\n// UI Builder Store\ninterface UIComponent {\n  id: string\n  type: string\n  props: Record<string, any>\n  position: { x: number; y: number }\n  size: { width: number; height: number }\n  parent_id?: string\n  children?: string[]\n}\n\ninterface Project {\n  id: string\n  name: string\n  description?: string\n  webhook_url?: string\n  is_published: boolean\n  public_url?: string\n  components: UIComponent[]\n}\n\ninterface UIBuilderState {\n  currentProject: Project | null\n  selectedComponent: string | null\n  draggedComponent: UIComponent | null\n  isPreviewMode: boolean\n  setCurrentProject: (project: Project | null) => void\n  setSelectedComponent: (componentId: string | null) => void\n  setDraggedComponent: (component: UIComponent | null) => void\n  togglePreviewMode: () => void\n  addComponent: (component: UIComponent) => void\n  updateComponent: (componentId: string, updates: Partial<UIComponent>) => void\n  removeComponent: (componentId: string) => void\n}\n\nexport const useUIBuilderStore = create<UIBuilderState>()(\n  devtools(\n    (set, get) => ({\n      currentProject: null,\n      selectedComponent: null,\n      draggedComponent: null,\n      isPreviewMode: false,\n      setCurrentProject: (project) => set({ currentProject: project }),\n      setSelectedComponent: (componentId) => set({ selectedComponent: componentId }),\n      setDraggedComponent: (component) => set({ draggedComponent: component }),\n      togglePreviewMode: () => set((state) => ({ isPreviewMode: !state.isPreviewMode })),\n      addComponent: (component) =>\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: [...state.currentProject.components, component],\n            },\n          }\n        }),\n      updateComponent: (componentId, updates) =>\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.map((comp) =>\n                comp.id === componentId ? { ...comp, ...updates } : comp\n              ),\n            },\n          }\n        }),\n      removeComponent: (componentId) =>\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.filter((comp) => comp.id !== componentId),\n            },\n          }\n        }),\n    }),\n    { name: 'ui-builder-store' }\n  )\n)\n\n// API Testing Store\ninterface APIRequest {\n  id: string\n  name: string\n  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'\n  url: string\n  headers: Record<string, string>\n  body?: string\n}\n\ninterface APITestingState {\n  requests: APIRequest[]\n  currentRequest: APIRequest | null\n  isLoading: boolean\n  lastResponse: any\n  addRequest: (request: APIRequest) => void\n  updateRequest: (requestId: string, updates: Partial<APIRequest>) => void\n  removeRequest: (requestId: string) => void\n  setCurrentRequest: (request: APIRequest | null) => void\n  setLoading: (loading: boolean) => void\n  setLastResponse: (response: any) => void\n}\n\nexport const useAPITestingStore = create<APITestingState>()(\n  devtools(\n    persist(\n      (set) => ({\n        requests: [],\n        currentRequest: null,\n        isLoading: false,\n        lastResponse: null,\n        addRequest: (request) =>\n          set((state) => ({ requests: [...state.requests, request] })),\n        updateRequest: (requestId, updates) =>\n          set((state) => ({\n            requests: state.requests.map((req) =>\n              req.id === requestId ? { ...req, ...updates } : req\n            ),\n          })),\n        removeRequest: (requestId) =>\n          set((state) => ({\n            requests: state.requests.filter((req) => req.id !== requestId),\n          })),\n        setCurrentRequest: (request) => set({ currentRequest: request }),\n        setLoading: (isLoading) => set({ isLoading }),\n        setLastResponse: (lastResponse) => set({ lastResponse }),\n      }),\n      {\n        name: 'api-testing-storage',\n        partialize: (state) => ({ requests: state.requests }),\n      }\n    ),\n    { name: 'api-testing-store' }\n  )\n)\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAiBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,QAAQ,IAAM,IAAI;gBAAE,MAAM;YAAK;IACjC,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,MAAM,MAAM,IAAI;QAAC,CAAC;AAC9C,IAEF;IAAE,MAAM;AAAa;AAuClB,MAAM,oBAAoB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,mBAAmB;QACnB,kBAAkB;QAClB,eAAe;QACf,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,sBAAsB,CAAC,cAAgB,IAAI;gBAAE,mBAAmB;YAAY;QAC5E,qBAAqB,CAAC,YAAc,IAAI;gBAAE,kBAAkB;YAAU;QACtE,mBAAmB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,eAAe,CAAC,MAAM,aAAa;gBAAC,CAAC;QAChF,cAAc,CAAC,YACb,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY;+BAAI,MAAM,cAAc,CAAC,UAAU;4BAAE;yBAAU;oBAC7D;gBACF;YACF;QACF,iBAAiB,CAAC,aAAa,UAC7B,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAC/C,KAAK,EAAE,KAAK,cAAc;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAExD;gBACF;YACF;QACF,iBAAiB,CAAC,cAChB,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;oBAC3E;gBACF;YACF;IACJ,CAAC,GACD;IAAE,MAAM;AAAmB;AA2BxB,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACrC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,cAAc;QACd,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAC5D,eAAe,CAAC,WAAW,UACzB,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,MAC5B,IAAI,EAAE,KAAK,YAAY;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEpD,CAAC;QACH,eAAe,CAAC,YACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;gBACtD,CAAC;QACH,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,iBAAiB,CAAC,eAAiB,IAAI;gBAAE;YAAa;IACxD,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,UAAU,MAAM,QAAQ;QAAC,CAAC;AACtD,IAEF;IAAE,MAAM;AAAoB", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useAuthStore } from '@/lib/store'\nimport { User } from '@/types'\n\nexport function useAuth() {\n  const router = useRouter()\n  const supabase = createClientComponentClient()\n  const { user, isLoading, setUser, setLoading } = useAuthStore()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession()\n        \n        if (error) {\n          console.error('Error getting session:', error)\n          setUser(null)\n        } else if (session?.user) {\n          // Fetch user profile from our database\n          const { data: profile } = await supabase\n            .from('users')\n            .select('*')\n            .eq('id', session.user.id)\n            .single()\n\n          if (profile) {\n            setUser({\n              id: profile.id,\n              email: profile.email,\n              subscription_tier: profile.subscription_tier,\n              created_at: profile.created_at,\n              updated_at: profile.updated_at,\n            })\n          } else {\n            // Create user profile if it doesn't exist\n            const newUser = {\n              id: session.user.id,\n              email: session.user.email!,\n              subscription_tier: 'free' as const,\n            }\n\n            const { error: insertError } = await supabase\n              .from('users')\n              .insert([newUser])\n\n            if (!insertError) {\n              setUser({\n                ...newUser,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n              })\n            }\n          }\n        } else {\n          setUser(null)\n        }\n      } catch (error) {\n        console.error('Error in getInitialSession:', error)\n        setUser(null)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          // Handle sign in\n          const { data: profile } = await supabase\n            .from('users')\n            .select('*')\n            .eq('id', session.user.id)\n            .single()\n\n          if (profile) {\n            setUser({\n              id: profile.id,\n              email: profile.email,\n              subscription_tier: profile.subscription_tier,\n              created_at: profile.created_at,\n              updated_at: profile.updated_at,\n            })\n          }\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          // Handle sign out\n          setUser(null)\n          router.push('/')\n        }\n      }\n    )\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [supabase, setUser, setLoading, router])\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          emailRedirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign up' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign out' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signInWithProvider = async (provider: 'google' | 'github') => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider,\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during OAuth sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during password reset' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return {\n    user,\n    isLoading,\n    signIn,\n    signUp,\n    signOut,\n    signInWithProvider,\n    resetPassword,\n    isAuthenticated: !!user,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AAQO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAE5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,sBAAsB;YACtB,MAAM;uDAAoB;oBACxB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;wBAEnE,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,0BAA0B;4BACxC,QAAQ;wBACV,OAAO,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;4BACxB,uCAAuC;4BACvC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;4BAET,IAAI,SAAS;gCACX,QAAQ;oCACN,IAAI,QAAQ,EAAE;oCACd,OAAO,QAAQ,KAAK;oCACpB,mBAAmB,QAAQ,iBAAiB;oCAC5C,YAAY,QAAQ,UAAU;oCAC9B,YAAY,QAAQ,UAAU;gCAChC;4BACF,OAAO;gCACL,0CAA0C;gCAC1C,MAAM,UAAU;oCACd,IAAI,QAAQ,IAAI,CAAC,EAAE;oCACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;oCACzB,mBAAmB;gCACrB;gCAEA,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC;oCAAC;iCAAQ;gCAEnB,IAAI,CAAC,aAAa;oCAChB,QAAQ;wCACN,GAAG,OAAO;wCACV,YAAY,IAAI,OAAO,WAAW;wCAClC,YAAY,IAAI,OAAO,WAAW;oCACpC;gCACF;4BACF;wBACF,OAAO;4BACL,QAAQ;wBACV;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC7C,QAAQ;oBACV,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;qCAChE,OAAO,OAAO;oBACZ,IAAI,UAAU,gBAAe,oBAAA,8BAAA,QAAS,IAAI,GAAE;wBAC1C,iBAAiB;wBACjB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;wBAET,IAAI,SAAS;4BACX,QAAQ;gCACN,IAAI,QAAQ,EAAE;gCACd,OAAO,QAAQ,KAAK;gCACpB,mBAAmB,QAAQ,iBAAiB;gCAC5C,YAAY,QAAQ,UAAU;gCAC9B,YAAY,QAAQ,UAAU;4BAChC;wBACF;wBACA,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,UAAU,cAAc;wBACjC,kBAAkB;wBAClB,QAAQ;wBACR,OAAO,IAAI,CAAC;oBACd;gBACF;;YAGF;qCAAO;oBACL,aAAa,WAAW;gBAC1B;;QACF;4BAAG;QAAC;QAAU;QAAS;QAAY;KAAO;IAE1C,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC3C;gBACA;gBACA,SAAS;oBACP,iBAAiB,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;gBAC7C;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD;gBACA,SAAS;oBACP,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;gBACxC;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;YACxC;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;AACF;GApNgB;;QACC,qIAAA,CAAA,YAAS;QAEyB,sHAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAIoD;QAJpD,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D,GAJpD;IAKb,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\n\nexport default function SignInPage() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState('')\n  const { signIn, signInWithProvider, isLoading } = useAuth()\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n\n    const result = await signIn(email, password)\n    if (!result.success) {\n      setError(result.error)\n    }\n  }\n\n  const handleOAuthSignIn = async (provider: 'google' | 'github') => {\n    setError('')\n    const result = await signInWithProvider(provider)\n    if (!result.success) {\n      setError(result.error)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"flex items-center justify-center space-x-2 mb-4\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg\"></div>\n            <span className=\"text-xl font-bold\">FlowUI</span>\n          </div>\n          <CardTitle>Welcome back</CardTitle>\n          <CardDescription>\n            Sign in to your account to continue building amazing interfaces\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                placeholder=\"Enter your password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n              {isLoading ? 'Signing in...' : 'Sign In'}\n            </Button>\n          </form>\n\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <span className=\"w-full border-t\" />\n            </div>\n            <div className=\"relative flex justify-center text-xs uppercase\">\n              <span className=\"bg-background px-2 text-muted-foreground\">\n                Or continue with\n              </span>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Button\n              variant=\"outline\"\n              onClick={() => handleOAuthSignIn('google')}\n              disabled={isLoading}\n            >\n              <svg className=\"mr-2 h-4 w-4\" viewBox=\"0 0 24 24\">\n                <path\n                  d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  fill=\"#4285F4\"\n                />\n                <path\n                  d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  fill=\"#34A853\"\n                />\n                <path\n                  d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  fill=\"#FBBC05\"\n                />\n                <path\n                  d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  fill=\"#EA4335\"\n                />\n              </svg>\n              Google\n            </Button>\n            <Button\n              variant=\"outline\"\n              onClick={() => handleOAuthSignIn('github')}\n              disabled={isLoading}\n            >\n              <svg className=\"mr-2 h-4 w-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              GitHub\n            </Button>\n          </div>\n\n          <div className=\"text-center text-sm\">\n            <span className=\"text-muted-foreground\">Don't have an account? </span>\n            <Link href=\"/auth/signup\" className=\"text-primary hover:underline\">\n              Sign up\n            </Link>\n          </div>\n\n          <div className=\"text-center text-sm\">\n            <Link href=\"/auth/forgot-password\" className=\"text-muted-foreground hover:underline\">\n              Forgot your password?\n            </Link>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACxD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,MAAM,SAAS,MAAM,OAAO,OAAO;QACnC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,SAAS,OAAO,KAAK;QACvB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,SAAS;QACT,MAAM,SAAS,MAAM,mBAAmB;QACxC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,SAAS,OAAO,KAAK;QACvB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAEtC,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;sCACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;sCAIvB,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,UAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,UAAU;;;;;;;;;;;;8CAGd,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,WAAU;oCAAS,UAAU;8CAChD,YAAY,kBAAkB;;;;;;;;;;;;sCAInC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAM/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,kBAAkB;oCACjC,UAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,SAAQ;;8DACpC,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;;;;;;;wCAEH;;;;;;;8CAGR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,kBAAkB;oCACjC,UAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAe,SAAQ;sDACxD,cAAA,6LAAC;gDACC,UAAS;gDACT,GAAE;gDACF,UAAS;;;;;;;;;;;wCAEP;;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAe,WAAU;8CAA+B;;;;;;;;;;;;sCAKrE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAwB,WAAU;0CAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjG;GAhJwB;;QAI4B,0HAAA,CAAA,UAAO;QAC1C,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}]}