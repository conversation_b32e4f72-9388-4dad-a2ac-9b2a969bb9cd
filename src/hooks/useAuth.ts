'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClientComponentClient } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import { User } from '@/types'

export function useAuth() {
  const router = useRouter()
  const supabase = createClientComponentClient()
  const { user, isLoading, setUser, setLoading } = useAuthStore()
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Set hydrated flag
    setIsHydrated(true)

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting session:', error)
          setUser(null)
        } else if (session?.user) {
          // Fetch user profile from our database
          const { data: profile } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single()

          if (profile) {
            setUser({
              id: profile.id,
              email: profile.email,
              subscription_tier: profile.subscription_tier,
              created_at: profile.created_at,
              updated_at: profile.updated_at,
            })
          } else {
            // Create user profile if it doesn't exist
            const newUser = {
              id: session.user.id,
              email: session.user.email!,
              subscription_tier: 'free' as const,
            }

            const { error: insertError } = await supabase
              .from('users')
              .insert([newUser])

            if (!insertError) {
              setUser({
                ...newUser,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })
            }
          }
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          // Handle sign in
          const { data: profile } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single()

          if (profile) {
            setUser({
              id: profile.id,
              email: profile.email,
              subscription_tier: profile.subscription_tier,
              created_at: profile.created_at,
              updated_at: profile.updated_at,
            })
          }
          router.push('/dashboard')
        } else if (event === 'SIGNED_OUT') {
          // Handle sign out
          setUser(null)
          router.push('/')
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase, setUser, setLoading, router])

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'An error occurred during sign in' 
      }
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string) => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) throw error
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'An error occurred during sign up' 
      }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'An error occurred during sign out' 
      }
    } finally {
      setLoading(false)
    }
  }

  const signInWithProvider = async (provider: 'google' | 'github') => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) throw error
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'An error occurred during OAuth sign in' 
      }
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) throw error
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'An error occurred during password reset' 
      }
    } finally {
      setLoading(false)
    }
  }

  return {
    user,
    isLoading: isLoading || !isHydrated,
    signIn,
    signUp,
    signOut,
    signInWithProvider,
    resetPassword,
    isAuthenticated: isHydrated && !!user,
  }
}
